import { PageStateResult } from "../../src/agent/types/extract-result";
import { GeminiLLMRepository } from "../../src/llm/GeminiLLMRepository";
import { PlatformTypes } from "../../src/ui/constants";
import { extractFormJSON } from "../../src/workflow/utils/helpers";
import { imageToBase64 } from "../common/ImageHelpers";
import { FORM_VISION_PROMPT_V6 } from "./geminiPerformance";

const GEMINI_API_KEY = ''; //TODO(Add API key here to run this script)
const GEMINI_BASE_URL = "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio";

const DEFAULT_TEST_ITERATIONS = 2; // Number of iterations for each test

async function loadScreenshot(fileName: string): Promise<string> {
    try {
        const screenshotPath = `${__dirname.replace('/scripts', `/files/screenshots/${fileName}`)}`;
        return await imageToBase64(screenshotPath);
    } catch (error) {
        console.error(`Failed to load screenshot ${fileName}:`, error);
        throw error;
    }
}

async function runTest(screenshotFileName: string, testName: string, platform: PlatformTypes, validate: (result: PageStateResult) => void): Promise<{ passed: number; failed: number }> {
    console.log(`\nRunning test: ${testName}`);
    const screenshot = await loadScreenshot(screenshotFileName);
    const geminiRepository = new GeminiLLMRepository(GEMINI_API_KEY, GEMINI_BASE_URL);
    let passed = 0;
    let failed = 0;

    for (let i = 0; i < DEFAULT_TEST_ITERATIONS; i++) {
        try {
            const response = await geminiRepository.getLLMResponse({
                platform: platform,
                prompt: FORM_VISION_PROMPT_V6,
                screenshot,
                skipCache: true,
                viewportWidth: 800,
                viewportHeight: 600,
            });
            const result = extractFormJSON(response.output_text);
            validate(result);
            passed++;
            console.log(`✅ ${testName}: Iteration ${i + 1} passed`);
        } catch (error) {
            failed++;
            console.error(`❌ [ Test ${testName}]: Iteration ${i + 1} failed:`, error);
        }
    }
    return { passed, failed };
}

// Critical Rule: Always Output Only the Required JSON Format
async function testJsonFormat(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "google_login.webp",
        "testJsonFormat()",
        "google",
        (result) => {
            if (
                typeof result !== "object" ||
                !result.formTitle ||
                !result.formDescription ||
                !result.errors ||
                !result.pageType ||
                !result.htmxForm ||
                !result.actions
            ) {
                throw new Error("Invalid JSON structure");
            }
            try {
                JSON.stringify(result);
            } catch {
                throw new Error("JSON is not serializable");
            }
        }
    );
}

// Critical Rule: Input Fields Must Be Empty
async function testEmptyInputs(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "google_login.webp",
        "testEmptyInputs()",
        "google",
        (result) => {
            const inputs = result.htmxForm.match(/<input[^>]*>/g) || [];
            inputs.forEach((input) => {
                if (!input.includes('value=""') && !input.includes("value=''") && !input.includes('placeholder=""')) {
                    throw new Error(`Input not empty: ${input}`);
                }
            });
        });
}

// Error Message Detection: Detect error messages in the form
async function testErrorMessagesInForm(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "google_wrong_password.png",
        "testErrorMessagesInForm()",
        "google",
        (result) => {
            if (!result.htmxForm.includes('<div class="form-error">')) {
                throw new Error("No form-error div in the form");
            }
        }
    );
}

// Error Message Detection: Add all error messages to the `errors` array
async function testErrorsDetection(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "github_incorrect_creds_error.png",
        "testErrorsDetection()",
        "github",
        (result) => {
            if (!Array.isArray(result.errors) || result.errors.length === 0) {
                throw new Error("No errors were detected in the 'errors' array.");
            }
        }
    );
}

// Exclusion Rule: Exclude "Forgot Password" or "Reset Password" or similar
async function testExcludeForgotPassword(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "facebook_login.webp",
        "testExcludeForgotPassword()",
        "facebook",
        (result) => {

            const forgotPasswordDetectionRegex = /\b(?:forgot(?:ten)?|reset)(?:\s+your)?\s+password\b\??/i;

            if (forgotPasswordDetectionRegex.test(result.htmxForm.toLowerCase())) {
                throw new Error("HTMX form contains 'Forgot Password' or 'Reset Password' or something similar.");
            }

            if (result.actions.some(action => forgotPasswordDetectionRegex.test(action.name.toLowerCase()))) {
                throw new Error("Actions contain 'Forgot Password' or 'Reset Password' or something similar actions.");
            }
        }
    );
}

// Exclusion Rule: Exclude "Create Account" or "Sign Up" or similar
async function testExcludeCreateAccount(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "facebook_login.webp",
        "testExcludeCreateAccount()",
        "facebook",
        (result) => {

            const createAccountDetectionRegex = /\b(?:(?:create|register)\s+(?:(?:a|an|new)\s+)*(?:account|one)|sign\s+up|register\s+yourself)\b/i;

            if (createAccountDetectionRegex.test(result.htmxForm.toLowerCase())) {
                throw new Error("HTMX form contains 'Create Account' or 'Sign Up' or something similar.");
            }

            if (result.actions.some(action => createAccountDetectionRegex.test(action.name.toLowerCase()))) {
                throw new Error("Actions contain 'Create Account' or 'Sign Up' or something similar actions.");
            }
        }
    );
}

// Exclusion Rule: Exclude "Remember me" or "Stay signed in" or similar
async function testExcludeRememberMe(): Promise<{ passed: number; failed: number }> {
    return runTest(
        "google-acknowledge.webp",
        "testExcludeRememberMe()",
        "google",
        (result) => {

            const rememberMeDetectionRegex = /\b(?:remember\s+me|(?:stay|keep\s+me)\s+(?:signed|logged)\s+in|(?:remember|trust)\s+(?:this\s+)?(?:device|computer|browser|pc)|(?:don't|do\s+not)\s+ask\s+again)\b/i;

            if (rememberMeDetectionRegex.test(result.htmxForm.toLowerCase())) {
                throw new Error("HTMX form contains 'Remember me' or 'Stay signed in' or something similar.");
            }

            if (result.actions.some(action => rememberMeDetectionRegex.test(action.name.toLowerCase()))) {
                throw new Error("Actions contain 'Remember me' or 'Stay signed in' or something similar actions.");
            }
        }
    );
}

async function runAllTests() {
    if (!GEMINI_API_KEY) {
        console.error("Gemini API key is missing.");
        throw new Error("Gemini API key is missing.");
    }

    const testFunctions = [
        testJsonFormat,
        testEmptyInputs,
        testErrorMessagesInForm,
        testErrorsDetection,
        testExcludeForgotPassword,
        testExcludeCreateAccount,
        testExcludeRememberMe,

    ];

    let passedOverallFunctionCount = 0;
    let partialFailedFunctionCount = 0;
    let failedOverallFunctionCount = 0;

    let passedIteration = 0;
    let failedIteration = 0;

    for (const testFn of testFunctions) {
        const { passed, failed } = await testFn();
        passedIteration += passed;
        failedIteration += failed;
        if (failed === 0) {
            passedOverallFunctionCount++;
        } else if (failed > 0 && passed > 0) {
            partialFailedFunctionCount++;
        }
        else {
            failedOverallFunctionCount++;
        }
    }

    console.log(`\nTest Summary:`);
    console.log(`Iterations: ${passedIteration} passed, ${failedIteration} failed`);
    console.log(`Functions: ${passedOverallFunctionCount} passed, ${partialFailedFunctionCount} partially failed, ${failedOverallFunctionCount} failed`);

    if (failedOverallFunctionCount > 0 || failedIteration > 0) {
        console.error("\nSome tests failed. Check logs for details.");
        process.exit(1);
    } else {
        console.log("\n✅ All tests passed!");
    }
}

runAllTests().catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
});