import { Hyperbrowser } from '@hyperbrowser/sdk';
import { RemoteBrowserService, BrowserSession } from '../adapters/BrowserDataAdapter';

/**
 * Hyperbrowser service implementation for remote browser sessions
 * Uses the Hyperbrowser SDK to manage remote browser instances
 */
export class HyperbrowserService implements RemoteBrowserService {
  private readonly hyperbrowser: Hyperbrowser;

  constructor(apiKey: string, timeout: number = 60000) {
    this.hyperbrowser = new Hyperbrowser({
      apiKey,
      timeout,
    });
  }

  /**
   * Create a new browser session using Hyperbrowser
   */
  async createSession(options?: {
    browserArgs?: string[];
    device?: ('desktop' | 'mobile')[];
    solveCaptchas?: boolean;
  }): Promise<BrowserSession> {
    console.log('→ Creating new Hyperbrowser session');

    try {
      const session = await this.hyperbrowser.sessions.create({
        browserArgs: options?.browserArgs || ['--auto-accept-this-tab-capture'],
        device: options?.device || ['desktop'],
        solveCaptchas: options?.solveCaptchas || false,
        useStealth: true,
      });

      if (!session.wsEndpoint) {
        throw new Error('Hyperbrowser session created but no WebSocket endpoint provided');
      }

      console.log(`✓ Successfully created Hyperbrowser session: ${session.id}`);

      return {
        wsEndpoint: session.wsEndpoint.concat('&keepAlive=true'),
        sessionId: session.id,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to create Hyperbrowser session:', errorMessage);
      throw new Error(`Failed to create Hyperbrowser session: ${errorMessage}`);
    }
  }

  /**
   * Get an existing browser session by ID
   */
  async getSession(sessionId: string): Promise<BrowserSession> {
    console.log(`→ Getting Hyperbrowser session: ${sessionId}`);

    try {
      const session = await this.hyperbrowser.sessions.get(sessionId);

      if (!session.wsEndpoint) {
        throw new Error(`Hyperbrowser session ${sessionId} does not have a WebSocket endpoint`);
      }

      console.log(`✓ Successfully retrieved Hyperbrowser session: ${sessionId}`);

      return {
        wsEndpoint: session.wsEndpoint.concat('&keepAlive=true'),
        sessionId: session.id,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Failed to get Hyperbrowser session ${sessionId}:`, errorMessage);
      throw new Error(`Failed to get Hyperbrowser session ${sessionId}: ${errorMessage}`);
    }
  }

  /**
   * Close a browser session
   */
  async closeSession(sessionId: string): Promise<void> {
    console.log(`→ Closing Hyperbrowser session: ${sessionId}`);

    try {
      await this.hyperbrowser.sessions.stop(sessionId);
      console.log(`✓ Successfully closed Hyperbrowser session: ${sessionId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Failed to close Hyperbrowser session ${sessionId}:`, errorMessage);
      throw new Error(`Failed to close Hyperbrowser session ${sessionId}: ${errorMessage}`);
    }
  }
}
