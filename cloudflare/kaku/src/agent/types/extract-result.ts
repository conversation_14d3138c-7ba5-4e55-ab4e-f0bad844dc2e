import { BoundingRect } from './agent-state';
import { FormVisionResult } from '../../form-generation/htmx-generator';

export type ExtractResult = {
  extractedData: PageStateResult;
  formVisionResult?: FormVisionResult;
};

export type Action = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  actor: 'ai' | 'human';
  value?: string;
  coordinates: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action without coordinates for Phase 1 fast response
export type ActionWithoutCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action with optional coordinates for intermediate processing
export type ActionWithOptionalCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  actor: 'ai' | 'human';
  value?: string;
  coordinates?: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

export type PageStateResult = {
  formTitle: string;
  formDescription: string;
  errors: string[];
  pageType: 'authenticated' | 'not-authenticated' | 'captcha' | 'loading' | 'other';

  htmxForm: string;
  actions: ActionWithOptionalCoordinates[];
};

export type PageStateResultWithOptionalCoordinates = {
  formTitle: string;
  formDescription: string;
  errors: string[];
  pageType: 'authenticated' | 'not-authenticated' | 'captcha' | 'loading' | 'other';
  htmxForm: string;
  actions: ActionWithOptionalCoordinates[];
  coordinatesResolved?: boolean; // Flag to track if coordinates are available
  coordinateResolutionPromise?: Promise<void>; // Promise for coordinate resolution
};

export type CaptchaBoundingBox = Omit<BoundingRect, 'id'>;
