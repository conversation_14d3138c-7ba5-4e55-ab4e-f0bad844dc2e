/**
 * Core form interfaces for HTMX form generation
 */

export interface FormVisionResult {
  screenInfo: FormMetadata;
  controls: FormControls;
}

export interface FormMetadata {
  title: string;
  description: string;
  instruction: string | null;
  verificationCode: string | null;
  errors: string[];
  type: 'authenticated' | 'not-authenticated' | 'captcha' | 'loading' | 'other';
}

export interface FormControls {
  fields: FormField[];
  buttons: FormButton[];
}

export interface FormField {
  id: string;
  order: number;
  actor: 'ai' | 'human';
  label: string;
  type:
    | 'text'
    | 'password'
    | 'email'
    | 'number'
    | 'checkbox'
    | 'checkboxgroup'
    | 'radio'
    | 'textarea'
    | 'other';
  actiontype: 'fill' | 'click' | 'select';
  name: string;
  checked?: boolean;
  options?: { value: string; label: string }[] | null;
  readOnly: boolean;
  box_2d?: [number, number, number, number] | null;
  point?: [number, number] | null;
}

export interface FormButton {
  id: string;
  order: number;
  actor: 'ai' | 'human';
  label: string;
  variant: 'primary' | 'secondary' | 'link';
  type: 'device-ack' | 'submit' | 'click';
  actiontype: 'click' | 'acknowledge';
  synthetic: boolean;
  box_2d?: [number, number, number, number] | null;
  point?: [number, number] | null;
}
