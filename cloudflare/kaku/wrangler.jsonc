{
  "name": "kaku",
  "main": "src/api/index.ts",
  "compatibility_date": "2025-03-17",
  "compatibility_flags": ["nodejs_compat"],
  "observability": {
    "enabled": true,
  },
  "vars": {
    "ENVIRONMENT": "local",
    "SUNNY_API_ENDPOINT": "http://localhost:8080",
    "KAKU_API_ENDPOINT": "http://localhost:8787",
    "KAKU_WS_ENDPOINT": "ws://localhost:8787",
    "AI_GATEWAY_OPENAI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai",
    "AI_GATEWAY_ANTHROPIC_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic",
    "AI_GATEWAY_GEMINI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio",
    "SKIP_CACHE": false,
    "ICE_SERVERS": [
      {
        "urls": "stun:stun.cloudflare.com:3478",
      },
    ],
  },
  "r2_buckets": [
    {
      "binding": "SCREENSHOTS_INBOUND_BUCKET",
      "bucket_name": "app-dev-kz-screenshots-inbound",
    },
  ],
  "env": {
    "dev": {
      "vars": {
        "ENVIRONMENT": "dev",
        "SUNNY_API_ENDPOINT": "https://dev-api.kazeel.com",
        "KAKU_API_ENDPOINT": "https://kaku-dev.pedro-alvarado.workers.dev",
        "KAKU_WS_ENDPOINT": "wss://kaku-dev.pedro-alvarado.workers.dev",
        "AI_GATEWAY_OPENAI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai",
        "AI_GATEWAY_ANTHROPIC_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic",
        "AI_GATEWAY_GEMINI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio",
        "SKIP_CACHE": false,
        "ICE_SERVERS": [
          {
            "urls": "stun:stun.cloudflare.com:3478",
          },
          {
            "urls": "turn:relay1.expressturn.com:3478", // 👈 this was missing 'turn:'
            "username": "ef89RMU4SHUQMSOUU9",
            "credential": "jvkMMnQxWX4Qrhe3",
          },
        ],
      },
      "r2_buckets": [
        {
          "binding": "SCREENSHOTS_INBOUND_BUCKET",
          "bucket_name": "app-dev-kz-screenshots-inbound",
        },
      ],
      "workflows": [
        {
          "name": "connections-workflow",
          "binding": "CONNECTIONS_WORKFLOW",
          "class_name": "ConnectionsWorkflow",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "Connections",
            "class_name": "Connections",
          },
          {
            "name": "USER_DATA_MANAGER_DURABLE_OBJECT",
            "class_name": "UserDataStoreManager",
            "script_name": "nico",
          },
        ],
      },
    },
    "prod": {
      "vars": {
        "ENVIRONMENT": "prod",
        "SUNNY_API_ENDPOINT": "https://api.kazeel.com",
        "KAKU_API_ENDPOINT": "undefined",
        "KAKU_WS_ENDPOINT": "undefined",
        "AI_GATEWAY_OPENAI_URL": "undefined",
        "AI_GATEWAY_ANTHROPIC_URL": "undefined",
        "AI_GATEWAY_GEMINI_URL": "undefined",
        "SKIP_CACHE": false,
        "ICE_SERVERS": [],
      },
      "r2_buckets": [
        {
          "binding": "SCREENSHOTS_INBOUND_BUCKET",
          "bucket_name": "app-primary-kz-screenshots-inbound",
        },
      ],
      "workflows": [
        {
          "name": "connections-workflow",
          "binding": "CONNECTIONS_WORKFLOW",
          "class_name": "ConnectionsWorkflow",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "Connections",
            "class_name": "Connections",
          },
          {
            "name": "USER_DATA_MANAGER_DURABLE_OBJECT",
            "class_name": "UserDataStoreManager",
            "script_name": "nico",
          },
        ],
      },
    },
  },
  "workflows": [
    {
      "name": "connections-workflow",
      "binding": "CONNECTIONS_WORKFLOW",
      "class_name": "ConnectionsWorkflow",
    },
  ],
  "durable_objects": {
    "bindings": [
      {
        "name": "Connections",
        "class_name": "Connections",
      },
      {
        "name": "USER_DATA_MANAGER_DURABLE_OBJECT",
        "class_name": "UserDataStoreManager",
        "script_name": "nico",
      },
    ],
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": ["Connections"],
    },
    {
      "tag": "v2",
      "new_sqlite_classes": ["UserDataStoreManager"],
    },
  ],
  "assets": {
    "directory": "./public/",
    "binding": "ASSETS",
  },
}
